/**
 * LazyRemixer - Main Application Class
 */
class LazyRemixer {
    constructor() {
        this.fileManager = new FileManager();
        this.videoProcessor = new VideoProcessor();
        this.uploadedFiles = [];
        this.currentSettings = {
            targetDuration: 60,
            quantity: 1,
            clipsPerVideo: 10
        };
        
        this.init();
    }

    /**
     * Initialize the application
     */
    init() {
        this.checkBrowserSupport();
        this.bindEvents();
        this.updateUI();
    }

    /**
     * Check browser support for required APIs
     */
    checkBrowserSupport() {
        if (!this.fileManager.isSupported()) {
            this.showError(
                'Your browser does not support the File System Access API. ' +
                'Please use Chrome 86+ or Edge 86+ for the best experience.'
            );
        }
    }

    /**
     * Bind event listeners
     */
    bindEvents() {
        // Folder selection
        document.getElementById('add-folder-btn').addEventListener('click', () => {
            this.selectVideoFolder();
        });

        // Download destination selection
        document.getElementById('select-destination-btn').addEventListener('click', () => {
            this.selectDownloadDestination();
        });

        // Settings
        const clipsSlider = document.getElementById('clips-per-video');
        const quantitySlider = document.getElementById('video-quantity');

        clipsSlider.addEventListener('input', (e) => {
            this.currentSettings.clipsPerVideo = parseInt(e.target.value);
            document.getElementById('clips-value').textContent = e.target.value;
            this.updateUI();
        });

        quantitySlider.addEventListener('input', (e) => {
            this.currentSettings.quantity = parseInt(e.target.value);
            document.getElementById('quantity-value').textContent = e.target.value;
            this.updateUI();
        });

        // Generation
        document.getElementById('generate-btn').addEventListener('click', () => {
            this.generateVideos();
        });

        // Cleanup
        document.getElementById('cleanup-btn').addEventListener('click', () => {
            this.cleanup();
        });

        // Clear folders
        document.getElementById('clear-folders-btn').addEventListener('click', () => {
            this.clearSourceFolders();
        });

        // Splitter video
        document.getElementById('select-splitter-btn').addEventListener('click', () => {
            this.selectSplitterVideo();
        });

        document.getElementById('remove-splitter-btn').addEventListener('click', () => {
            this.removeSplitterVideo();
        });

        // Modal events
        document.getElementById('close-error-modal').addEventListener('click', () => {
            this.hideError();
        });

        document.getElementById('error-ok-btn').addEventListener('click', () => {
            this.hideError();
        });

        // Close modal on outside click
        document.getElementById('error-modal').addEventListener('click', (e) => {
            if (e.target.id === 'error-modal') {
                this.hideError();
            }
        });
    }

    /**
     * Select splitter video
     */
    async selectSplitterVideo() {
        try {
            this.showLoading('Selecting splitter video...');
            const file = await this.fileManager.selectSplitterVideo();

            if (file) {
                this.splitterVideo = file;
                document.getElementById('splitter-name').textContent = file.name;
                document.getElementById('splitter-info').classList.remove('hidden');
            }

            this.hideLoading();

        } catch (error) {
            this.hideLoading();
            this.showError(error.message);
        }
    }

    /**
     * Remove splitter video
     */
    removeSplitterVideo() {
        this.splitterVideo = null;
        document.getElementById('splitter-name').textContent = 'No splitter selected';
        document.getElementById('splitter-info').classList.add('hidden');
    }

    /**
     * Select video folder (scan only, no upload)
     */
    async selectVideoFolder() {
        try {
            this.showLoading('Scanning for video files...');

            const result = await this.fileManager.selectVideoDirectory();

            // Update UI with scan results
            const container = document.getElementById('folders-container');
            const sourceSummary = document.getElementById('source-summary');
            const clearBtn = document.getElementById('clear-folders-btn');

            // Add folder to UI
            const folderItem = document.createElement('div');
            folderItem.className = 'folder-item';
            folderItem.innerHTML = `
                <div class="folder-item-info">
                    <div class="folder-item-name">${result.directoryName}</div>
                    <div class="folder-item-count">${result.videoCount} videos</div>
                </div>
            `;
            container.appendChild(folderItem);

            // Update summary
            document.getElementById('folder-count').textContent = '1';
            document.getElementById('video-count').textContent = result.videoCount;
            sourceSummary.classList.remove('hidden');
            clearBtn.classList.remove('hidden');

            // Clear any previous uploads since we're selecting a new folder
            this.uploadedFiles = [];

            this.hideLoading();
            this.updateUI();

            // Show helpful message about deferred upload
            this.showNotification(`Found ${result.videoCount} video files. Videos will be uploaded when you click "Generate Videos".`);

        } catch (error) {
            this.hideLoading();
            this.showError(error.message);
        }
    }

    /**
     * Select download destination
     */
    async selectDownloadDestination() {
        try {
            const result = await this.fileManager.selectDownloadDirectory();
            
            document.getElementById('destination-name').textContent = result.directoryName;
            document.getElementById('destination-info').classList.remove('hidden');
            
        } catch (error) {
            this.showError(error.message);
        }
    }

    /**
     * Generate videos (with deferred upload)
     */
    async generateVideos() {
        // Prevent multiple simultaneous generations
        if (this.videoProcessor.getStatus().isProcessing) {
            this.showError('Video generation is already in progress. Please wait for it to complete.');
            return;
        }

        // Check if folder is selected
        if (!this.fileManager.videoFiles || this.fileManager.videoFiles.length === 0) {
            this.showError('Please select a video folder first.');
            return;
        }

        const generateBtn = document.getElementById('generate-btn');

        try {
            // Disable button immediately to prevent double-clicks
            generateBtn.disabled = true;
            generateBtn.innerHTML = '<span class="btn-icon">⏳</span>Uploading...';

            // Show progress container
            document.getElementById('progress-container').classList.remove('hidden');
            document.getElementById('results-container').classList.add('hidden');

            // Clear previous results
            document.getElementById('video-progress-list').innerHTML = '';
            document.getElementById('results-list').innerHTML = '';

            // Update status
            document.getElementById('progress-title').textContent = 'Uploading video files...';
            document.getElementById('progress-status').textContent = 'Preparing files for upload...';

            // Upload files first (deferred upload)
            this.uploadedFiles = await this.fileManager.uploadVideos((progress, message) => {
                document.getElementById('progress-status').textContent = message;
            });

            // Update button text for generation phase
            generateBtn.innerHTML = '<span class="btn-icon">⏳</span>Generating...';

            // Now start video generation
            await this.videoProcessor.generateVideos(
                this.uploadedFiles,
                this.currentSettings,
                {
                    onStart: () => {
                        document.getElementById('progress-title').textContent =
                            `Generating ${this.currentSettings.quantity} video(s)...`;
                        document.getElementById('progress-status').textContent =
                            'Starting video generation...';
                    },
                    onProgress: (data) => {
                        this.updateProgress(data);
                    },
                    onVideoReady: (video, videoNumber) => {
                        this.handleVideoReady(video, videoNumber);
                    },
                    onComplete: (videos) => {
                        this.handleGenerationComplete(videos);
                    },
                    onError: (error) => {
                        this.showError(error.message);
                    }
                }
            );

        } catch (error) {
            this.showError(error.message);
        } finally {
            // Reset generate button
            generateBtn.disabled = false;
            generateBtn.innerHTML = '<span class="btn-icon">🎲</span>Generate Random Videos';
        }
    }

    /**
     * Update progress display
     */
    updateProgress(data) {
        const progressList = document.getElementById('video-progress-list');
        let progressItem = document.getElementById(`progress-${data.videoNumber}`);
        
        if (!progressItem) {
            progressItem = this.createProgressItem(data.videoNumber);
            progressList.appendChild(progressItem);
        }
        
        // Update progress
        const progressFill = progressItem.querySelector('.progress-fill');
        const progressPercentage = progressItem.querySelector('.progress-percentage');
        const progressMessage = progressItem.querySelector('.progress-message');
        
        progressFill.style.width = `${data.progress}%`;
        progressPercentage.textContent = `${Math.round(data.progress)}%`;
        progressMessage.textContent = data.message;
        
        // Update overall status
        document.getElementById('progress-status').textContent = 
            `Video ${data.videoNumber} - ${data.message}`;
    }

    /**
     * Create progress item element
     */
    createProgressItem(videoNumber) {
        const item = document.createElement('div');
        item.className = 'video-progress-item';
        item.id = `progress-${videoNumber}`;
        
        item.innerHTML = `
            <div class="progress-item-header">
                <span class="progress-item-title">Video ${videoNumber}</span>
                <span class="progress-percentage">0%</span>
            </div>
            <div class="progress-bar">
                <div class="progress-fill" style="width: 0%"></div>
            </div>
            <div class="progress-message">Initializing...</div>
        `;
        
        return item;
    }

    /**
     * Handle video ready event
     */
    async handleVideoReady(video, videoNumber) {
        // Mark progress as completed
        const progressItem = document.getElementById(`progress-${videoNumber}`);
        if (progressItem) {
            progressItem.classList.add('completed');
            progressItem.querySelector('.progress-fill').style.width = '100%';
            progressItem.querySelector('.progress-percentage').textContent = '100%';
            progressItem.querySelector('.progress-message').textContent = 'Complete!';
        }
        
        // Download video immediately
        try {
            const result = await this.fileManager.downloadVideo(video);
            this.showNotification(result.message);
        } catch (error) {
            console.error('Download error:', error);
            this.showError(`Failed to download video ${videoNumber}: ${error.message}`);
        }
        
        // Add to results
        this.addVideoResult(video, videoNumber);
    }

    /**
     * Add video to results display
     */
    addVideoResult(video, videoNumber) {
        const resultsList = document.getElementById('results-list');
        const resultsContainer = document.getElementById('results-container');
        
        resultsContainer.classList.remove('hidden');
        
        const resultItem = document.createElement('div');
        resultItem.className = 'result-item';
        
        resultItem.innerHTML = `
            <div class="result-header">
                <span class="result-title">Video ${videoNumber}</span>
                <span class="result-status success">Ready</span>
            </div>
            <div class="result-info">
                <div class="result-info-item">
                    <span class="label">Filename:</span>
                    <span class="value">${video.filename}</span>
                </div>
                <div class="result-info-item">
                    <span class="label">Size:</span>
                    <span class="value">${this.videoProcessor.formatFileSize(video.size)}</span>
                </div>
                <div class="result-info-item">
                    <span class="label">Clips:</span>
                    <span class="value">${video.clipCount}</span>
                </div>
            </div>
            <div class="result-actions">
                <button class="btn btn-primary btn-small" onclick="app.downloadVideoAgain('${video.filename}')">
                    <span class="btn-icon">💾</span>Download Again
                </button>
            </div>
        `;
        
        resultsList.appendChild(resultItem);
    }

    /**
     * Handle generation complete
     */
    handleGenerationComplete(videos) {
        document.getElementById('progress-status').textContent =
            `Complete! Generated ${videos.length} video(s)`;

        const stats = this.videoProcessor.getStatistics();
        if (stats) {
            this.showNotification(
                `Generation complete! Created ${stats.videoCount} videos with ${stats.totalClips} total clips.`
            );
        }
    }

    /**
     * Download video again
     */
    async downloadVideoAgain(filename) {
        try {
            const video = { filename };
            const result = await this.fileManager.downloadVideo(video);
            this.showNotification(result.message);
        } catch (error) {
            this.showError(`Failed to download video: ${error.message}`);
        }
    }

    /**
     * Clear all source folders
     */
    clearSourceFolders() {
        this.fileManager.clear(); // Clear the file manager state
        document.getElementById('folders-container').innerHTML = '';
        document.getElementById('source-summary').classList.add('hidden');
        document.getElementById('clear-folders-btn').classList.add('hidden');
        this.updateUI();
    }

    /**
     * Cleanup files
     */
    async cleanup() {
        if (confirm('Are you sure you want to delete all uploaded and generated files?')) {
            try {
                this.showLoading('Cleaning up files...');

                const result = await this.videoProcessor.cleanup();

                // Clear UI and file manager state
                this.uploadedFiles = [];
                this.fileManager.clear(); // Clear the file manager state
                document.getElementById('folder-info').classList.add('hidden');
                document.getElementById('progress-container').classList.add('hidden');
                document.getElementById('results-container').classList.add('hidden');

                this.hideLoading();
                this.updateUI();

                this.showNotification(result.message);

            } catch (error) {
                this.hideLoading();
                this.showError(error.message);
            }
        }
    }


        // Settings sliders
        const clipsSlider = document.getElementById('clips-per-video');
        const clipsValue = document.getElementById('clips-value');
        clipsSlider.addEventListener('input', () => {
            clipsValue.textContent = clipsSlider.value;
        });

        const quantitySlider = document.getElementById('video-quantity');
        const quantityValue = document.getElementById('quantity-value');
        quantitySlider.addEventListener('input', () => {
            quantityValue.textContent = quantitySlider.value;
        });

        // Generation buttons
        document.getElementById('generate-btn').addEventListener('click', () => this.generateVideos());
        document.getElementById('cleanup-btn').addEventListener('click', () => this.cleanupFiles());

        // Error modal
        document.getElementById('error-ok-btn').addEventListener('click', () => this.hideErrorModal());
        document.getElementById('close-error-modal').addEventListener('click', () => this.hideErrorModal());

        // Destination folder
        document.getElementById('select-destination-btn').addEventListener('click', () => this.selectDestinationFolder());
    }

    /**
     * Add a source folder for videos
     */
    async addSourceFolder() {
        try {
            this.showLoading('Scanning folder for videos...');
            const folderInfo = await this.fileManager.addVideoFolder();
            this.hideLoading();

            if (!folderInfo) return; // User cancelled

            this.sourceFolders.push(folderInfo);
            this.updateSourceFoldersUI();
            this.updateGenerateButtonState();

        } catch (error) {
            this.hideLoading();
            this.showError(`Failed to add folder: ${error.message}`);
        }
    }

    /**
     * Update the UI to show all source folders
     */
    updateSourceFoldersUI() {
        const container = document.getElementById('folders-container');
        const sourceSummary = document.getElementById('source-summary');
        const clearBtn = document.getElementById('clear-folders-btn');

        // Clear current folders
        container.innerHTML = '';

        // Add each folder
        this.sourceFolders.forEach((folder, index) => {
            const folderItem = document.createElement('div');
            folderItem.className = 'folder-item';
            folderItem.innerHTML = `
                <div class="folder-item-info">
                    <div class="folder-item-name">${folder.name}</div>
                    <div class="folder-item-count">${folder.fileCount} videos</div>
                </div>
                <div class="folder-item-actions">
                    <button class="btn btn-small remove-folder-btn" data-index="${index}">
                        <span class="btn-icon">❌</span>
                    </button>
                </div>
            `;
            container.appendChild(folderItem);

            // Add remove event
            folderItem.querySelector('.remove-folder-btn').addEventListener('click', (e) => {
                const idx = parseInt(e.currentTarget.dataset.index, 10);
                this.removeSourceFolder(idx);
            });
        });

        // Update summary
        if (this.sourceFolders.length > 0) {
            const totalVideos = this.sourceFolders.reduce((sum, folder) => sum + folder.fileCount, 0);
            document.getElementById('folder-count').textContent = this.sourceFolders.length;
            document.getElementById('video-count').textContent = totalVideos;
            sourceSummary.classList.remove('hidden');
            clearBtn.classList.remove('hidden');
        } else {
            sourceSummary.classList.add('hidden');
            clearBtn.classList.add('hidden');
        }
    }

    /**
     * Remove a source folder
     */
    removeSourceFolder(index) {
        if (index >= 0 && index < this.sourceFolders.length) {
            this.sourceFolders.splice(index, 1);
            this.fileManager.removeFolder(index);
            this.updateSourceFoldersUI();
            this.updateGenerateButtonState();
        }
    }

    /**
     * Clear all source folders
     */
    clearSourceFolders() {
        this.sourceFolders = [];
        this.fileManager.clearFolders();
        this.updateSourceFoldersUI();
        this.updateGenerateButtonState();
    }

    /**
     * Select a splitter video
     */
    async selectSplitterVideo() {
        try {
            this.showLoading('Selecting splitter video...');
            const splitterInfo = await this.fileManager.selectSplitterVideo();
            this.hideLoading();

            if (!splitterInfo) return; // User cancelled

            this.splitterVideo = splitterInfo;
            this.updateSplitterUI();

        } catch (error) {
            this.hideLoading();
            this.showError(`Failed to select splitter video: ${error.message}`);
        }
    }

    /**
     * Update splitter video UI
     */
    updateSplitterUI() {
        const splitterInfo = document.getElementById('splitter-info');
        const splitterName = document.getElementById('splitter-name');

        if (this.splitterVideo) {
            splitterName.textContent = this.splitterVideo.name;
            splitterInfo.classList.remove('hidden');
        } else {
            splitterName.textContent = 'No splitter selected';
            splitterInfo.classList.add('hidden');
        }
    }

    /**
     * Remove the splitter video
     */
    removeSplitterVideo() {
        this.splitterVideo = null;
        this.fileManager.removeSplitterVideo();
        this.updateSplitterUI();
    }

    /**
     * Select destination folder
     */
    async selectDestinationFolder() {
        try {
            this.showLoading('Selecting destination folder...');
            const folderInfo = await this.fileManager.selectDownloadFolder();
            this.hideLoading();

            if (!folderInfo) return; // User cancelled

            document.getElementById('destination-name').textContent = folderInfo.name;
            document.getElementById('destination-info').classList.remove('hidden');

        } catch (error) {
            this.hideLoading();
            this.showError(`Failed to select destination folder: ${error.message}`);
        }
    }

    /**
     * Update generate button state based on available videos
     */
    updateGenerateButtonState() {
        const generateBtn = document.getElementById('generate-btn');
        generateBtn.disabled = this.isProcessing || this.sourceFolders.length === 0;
    }

    /**
     * Generate videos
     */
    async generateVideos() {
        if (this.isProcessing || this.sourceFolders.length === 0) return;

        try {
            this.isProcessing = true;
            this.updateGenerateButtonState();
            this.showProgress();

            // Get settings
            const clipsPerVideo = parseInt(document.getElementById('clips-per-video').value, 10);
            const quantity = parseInt(document.getElementById('video-quantity').value, 10);

            // Upload videos
            this.updateProgressStatus('Uploading videos...');
            const uploadedFiles = await this.fileManager.uploadFiles((progress, message) => {
                this.updateProgressStatus(message);
            });

            // Generate videos
            this.updateProgressStatus('Generating videos...');
            await this.videoProcessor.generateVideos(uploadedFiles, {
                clipsPerVideo,
                quantity
            }, {
                onStart: () => {
                    this.clearProgressList();
                    this.updateProgressStatus('Starting video generation...');
                },
                onProgress: (data) => {
                    this.updateVideoProgress(data);
                },
                onVideoReady: (video) => {
                    this.addVideoResult(video);
                },
                onComplete: (videos) => {
                    this.showResults();
                    this.updateProgressStatus(`${videos.length} videos generated successfully!`);
                },
                onError: (error) => {
                    this.showError(error.message);
                }
            });

        } catch (error) {
            this.showError(`Failed to generate videos: ${error.message}`);
        } finally {
            this.isProcessing = false;
            this.updateGenerateButtonState();
        }
    }

    /**
     * Clean up server files
     */
    async cleanupFiles() {
        if (this.isProcessing) return;

        try {
            this.showLoading('Cleaning up files...');
            await this.videoProcessor.cleanup();
            this.hideLoading();

            // Clear results
            document.getElementById('results-list').innerHTML = '';
            document.getElementById('results-container').classList.add('hidden');

        } catch (error) {
            this.hideLoading();
            this.showError(`Failed to cleanup files: ${error.message}`);
        }
    }

    /**
     * Show loading overlay
     */
    showLoading(message = 'Loading...') {
        document.getElementById('loading-message').textContent = message;
        document.getElementById('loading-overlay').classList.remove('hidden');
    }

    /**
     * Hide loading overlay
     */
    hideLoading() {
        document.getElementById('loading-overlay').classList.add('hidden');
    }

    /**
     * Show error modal
     */
    showError(message) {
        document.getElementById('error-message').textContent = message;
        document.getElementById('error-modal').classList.remove('hidden');
    }

    /**
     * Hide error modal
     */
    hideErrorModal() {
        document.getElementById('error-modal').classList.add('hidden');
    }

    /**
     * Show progress container
     */
    showProgress() {
        document.getElementById('progress-container').classList.remove('hidden');
    }

    /**
     * Update progress status
     */
    updateProgressStatus(message) {
        document.getElementById('progress-status').textContent = message;
    }

    /**
     * Clear progress list
     */
    clearProgressList() {
        document.getElementById('video-progress-list').innerHTML = '';
    }

    /**
     * Update video progress
     */
    updateVideoProgress(data) {
        const listEl = document.getElementById('video-progress-list');
        let progressItem = document.getElementById(`progress-item-${data.videoNumber}`);

        if (!progressItem) {
            progressItem = document.createElement('div');
            progressItem.id = `progress-item-${data.videoNumber}`;
            progressItem.className = 'progress-item';
            progressItem.innerHTML = `
                <div class="progress-item-header">
                    <span class="progress-item-title">Video ${data.videoNumber}</span>
                    <span class="progress-item-status"></span>
                </div>
                <div class="progress-bar">
                    <div class="progress-bar-fill" style="width: 0%"></div>
                </div>
            `;
            listEl.appendChild(progressItem);
        }

        // Update progress
        const statusEl = progressItem.querySelector('.progress-item-status');
        const fillEl = progressItem.querySelector('.progress-bar-fill');

        statusEl.textContent = data.message || `${data.progress}%`;
        fillEl.style.width = `${data.progress}%`;
    }

    /**
     * Show results container
     */
    showResults() {
        document.getElementById('results-container').classList.remove('hidden');
    }

    /**
     * Add video result
     */
    addVideoResult(video) {
        const listEl = document.getElementById('results-list');
        const resultItem = document.createElement('div');
        resultItem.className = 'result-item';

        // Format size and duration
        const size = this.formatFileSize(video.size);
        const duration = this.formatDuration(video.duration);

        resultItem.innerHTML = `
            <div class="result-item-info">
                <div class="result-item-title">Video ${video.id.substring(0, 8)}</div>
                <div class="result-item-details">
                    <span>${size}</span>
                    <span>${duration}</span>
                    <span>${video.clipCount} clips</span>
                </div>
            </div>
            <div class="result-item-actions">
                <a href="/api/download/${video.filename}" class="btn btn-small btn-primary" download>
                    <span class="btn-icon">⬇️</span>
                    Download
                </a>
            </div>
        `;

        listEl.appendChild(resultItem);
    }

    /**
     * Format file size
     */
    formatFileSize(bytes) {
        return this.videoProcessor.formatFileSize(bytes);
    }

    /**
     * Format duration
     */
    formatDuration(seconds) {
        return this.videoProcessor.formatDuration(seconds);
    }
}

// Initialize app when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.app = new LazyRemixer();
});
    /**
     * Update UI state
     */
    updateUI() {
        const generateBtn = document.getElementById('generate-btn');
        const hasSelectedFolder = this.fileManager.videoFiles && this.fileManager.videoFiles.length > 0;

        // Enable generate button if folder is selected and not currently processing
        generateBtn.disabled = !hasSelectedFolder || this.videoProcessor.getStatus().isProcessing;

        // Update settings display
        document.getElementById('clips-value').textContent = this.currentSettings.clipsPerVideo;
        document.getElementById('quantity-value').textContent = this.currentSettings.quantity;
    }

    /**
     * Show loading overlay
     */
    showLoading(message = 'Loading...') {
        document.getElementById('loading-message').textContent = message;
        document.getElementById('loading-overlay').classList.remove('hidden');
    }

    /**
     * Hide loading overlay
     */
    hideLoading() {
        document.getElementById('loading-overlay').classList.add('hidden');
    }

    /**
     * Show error modal
     */
    showError(message) {
        document.getElementById('error-message').textContent = message;
        document.getElementById('error-modal').classList.remove('hidden');
    }

    /**
     * Hide error modal
     */
    hideError() {
        document.getElementById('error-modal').classList.add('hidden');
    }

    /**
     * Show notification (simple implementation)
     */
    showNotification(message) {
        // Create a simple notification
        const notification = document.createElement('div');
        notification.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: var(--accent-secondary);
            color: white;
            padding: 15px 20px;
            border-radius: 8px;
            box-shadow: var(--shadow-lg);
            z-index: 1002;
            max-width: 300px;
            word-wrap: break-word;
        `;
        notification.textContent = message;

        document.body.appendChild(notification);

        // Auto remove after 5 seconds
        setTimeout(() => {
            if (notification.parentNode) {
                notification.parentNode.removeChild(notification);
            }
        }, 5000);

        // Add click to dismiss
        notification.addEventListener('click', () => {
            if (notification.parentNode) {
                notification.parentNode.removeChild(notification);
            }
        });
    }
}

// Initialize application when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.app = new LazyRemixer();
});

// Global error handler
window.addEventListener('error', (event) => {
    console.error('Global error:', event.error);
    if (window.app) {
        window.app.showError(`Unexpected error: ${event.error.message}`);
    }
});

// Handle unhandled promise rejections
window.addEventListener('unhandledrejection', (event) => {
    console.error('Unhandled promise rejection:', event.reason);
    if (window.app) {
        window.app.showError(`Unexpected error: ${event.reason.message || event.reason}`);
    }
    event.preventDefault();
});
