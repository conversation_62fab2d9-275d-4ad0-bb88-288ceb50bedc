/**
 * FileManager - Handles file system operations using the File System Access API
 */
class FileManager {
    constructor() {
        this.videoFiles = [];
        this.videoDirectoryHandle = null;
        this.downloadDirectoryHandle = null;
        this.supportedExtensions = ['.mp4', '.avi', '.mov', '.mkv', '.webm'];
    }

    /**
     * Check if File System Access API is supported
     */
    isSupported() {
        return 'showDirectoryPicker' in window;
    }

    /**
     * Select a directory containing video files (scan only, no upload)
     */
    async selectVideoDirectory() {
        if (!this.isSupported()) {
            throw new Error('File System Access API is not supported in this browser. Please use Chrome 86+ or Edge 86+.');
        }

        try {
            // Ensure we're calling the method on the window object directly
            if (typeof window.showDirectoryPicker !== 'function') {
                throw new Error('The showDirectoryPicker API is not available in your browser.');
            }

            this.videoDirectoryHandle = await window.showDirectoryPicker({
                mode: 'read'
            });

            this.videoFiles = await this.scanForVideoFiles(this.videoDirectoryHandle);

            if (this.videoFiles.length === 0) {
                throw new Error('No supported video files found in the selected directory.');
            }

            console.log(`Scanned directory: ${this.videoDirectoryHandle.name}, found ${this.videoFiles.length} video files`);

            return {
                directoryName: this.videoDirectoryHandle.name,
                videoCount: this.videoFiles.length,
                files: this.videoFiles,
                totalSize: this.videoFiles.reduce((sum, file) => sum + file.size, 0)
            };
        } catch (error) {
            if (error.name === 'AbortError') {
                throw new Error('Directory selection was cancelled.');
            }
            throw error;
        }
    }

    /**
     * Select a directory for downloading generated videos
     */
    async selectDownloadDirectory() {
        if (!this.isSupported()) {
            throw new Error('File System Access API is not supported in this browser.');
        }

        try {
            this.downloadDirectoryHandle = await window.showDirectoryPicker({
                mode: 'readwrite'
            });

            return {
                directoryName: this.downloadDirectoryHandle.name
            };
        } catch (error) {
            if (error.name === 'AbortError') {
                throw new Error('Download directory selection was cancelled.');
            }
            throw error;
        }
    }

    /**
     * Scan directory for video files recursively
     */
    async scanForVideoFiles(directoryHandle, maxDepth = 2, currentDepth = 0) {
        const videoFiles = [];

        if (currentDepth >= maxDepth) {
            return videoFiles;
        }

        try {
            for await (const [name, handle] of directoryHandle.entries()) {
                if (handle.kind === 'file') {
                    const extension = this.getFileExtension(name);
                    if (this.supportedExtensions.includes(extension)) {
                        try {
                            const file = await handle.getFile();
                            videoFiles.push({
                                name: name,
                                file: file,
                                handle: handle,
                                size: file.size,
                                lastModified: file.lastModified,
                                path: this.getRelativePath(directoryHandle, name)
                            });
                        } catch (error) {
                            console.warn(`Could not access file ${name}:`, error);
                        }
                    }
                } else if (handle.kind === 'directory' && currentDepth < maxDepth - 1) {
                    // Recursively scan subdirectories
                    const subFiles = await this.scanForVideoFiles(handle, maxDepth, currentDepth + 1);
                    videoFiles.push(...subFiles);
                }
            }
        } catch (error) {
            console.error('Error scanning directory:', error);
        }

        return videoFiles;
    }

    /**
     * Get file extension in lowercase
     */
    getFileExtension(filename) {
        return filename.toLowerCase().substring(filename.lastIndexOf('.'));
    }

    /**
     * Get relative path for display purposes
     */
    getRelativePath(directoryHandle, filename) {
        return filename; // Simplified for now
    }

    /**
     * Upload video files to the server (called during generation)
     */
    async uploadVideos(progressCallback) {
        if (!this.videoFiles || this.videoFiles.length === 0) {
            throw new Error('No video files selected');
        }

        console.log(`Starting upload of ${this.videoFiles.length} video files`);

        const formData = new FormData();

        // Add files to form data with progress tracking
        for (let i = 0; i < this.videoFiles.length; i++) {
            const videoFile = this.videoFiles[i];
            formData.append('videos', videoFile.file, videoFile.name);

            if (progressCallback) {
                const progress = ((i + 1) / this.videoFiles.length) * 100;
                progressCallback(progress, `Preparing ${videoFile.name} (${this.formatFileSize(videoFile.size)})...`);
            }
        }

        if (progressCallback) {
            progressCallback(100, 'Uploading files to server...');
        }

        try {
            const response = await fetch('/api/upload-videos', {
                method: 'POST',
                body: formData
            });

            if (!response.ok) {
                const errorData = await response.json();
                throw new Error(errorData.error || 'Upload failed');
            }

            const result = await response.json();
            console.log(`Upload completed: ${result.files.length} files uploaded successfully`);
            return result.files;
        } catch (error) {
            console.error('Upload error:', error);
            throw new Error(`Failed to upload videos: ${error.message}`);
        }
    }

    /**
     * Download a generated video to the selected directory
     */
    async downloadVideo(videoData) {
        if (!this.downloadDirectoryHandle) {
            // Fallback to regular download if no directory selected
            return this.downloadVideoFallback(videoData);
        }

        try {
            // Fetch the video file from server
            const response = await fetch(`/api/download/${videoData.filename}`);
            if (!response.ok) {
                throw new Error('Failed to fetch video from server');
            }

            const blob = await response.blob();
            
            // Create file in selected directory
            const fileHandle = await this.downloadDirectoryHandle.getFileHandle(
                videoData.filename,
                { create: true }
            );

            const writable = await fileHandle.createWritable();
            await writable.write(blob);
            await writable.close();

            return {
                success: true,
                message: `Video saved to ${this.downloadDirectoryHandle.name}/${videoData.filename}`
            };
        } catch (error) {
            console.error('Download error:', error);
            // Fallback to regular download
            return this.downloadVideoFallback(videoData);
        }
    }

    /**
     * Fallback download method using regular browser download
     */
    async downloadVideoFallback(videoData) {
        try {
            const response = await fetch(`/api/download/${videoData.filename}`);
            if (!response.ok) {
                throw new Error('Failed to fetch video from server');
            }

            const blob = await response.blob();
            const url = URL.createObjectURL(blob);
            
            const a = document.createElement('a');
            a.href = url;
            a.download = videoData.filename;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            
            URL.revokeObjectURL(url);

            return {
                success: true,
                message: `Video downloaded: ${videoData.filename}`
            };
        } catch (error) {
            console.error('Fallback download error:', error);
            throw new Error(`Failed to download video: ${error.message}`);
        }
    }

    /**
     * Format file size for display
     */
    formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';
        
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    /**
     * Get summary of selected files
     */
    getFilesSummary() {
        if (!this.videoFiles || this.videoFiles.length === 0) {
            return null;
        }

        const totalSize = this.videoFiles.reduce((sum, file) => sum + file.size, 0);
        const extensions = [...new Set(this.videoFiles.map(file => this.getFileExtension(file.name)))];

        return {
            count: this.videoFiles.length,
            totalSize: this.formatFileSize(totalSize),
            extensions: extensions,
            directoryName: this.videoDirectoryHandle?.name || 'Unknown'
        };
    }

    /**
     * Clear selected files and directories
     */
    clear() {
        this.videoFiles = [];
        this.videoDirectoryHandle = null;
        this.downloadDirectoryHandle = null;
    }

    /**
     * Add a video folder (compatibility with newer UI)
     */
    async addVideoFolder() {
        return await this.selectVideoDirectory();
    }

    /**
     * Select a single video file to use as a splitter
     */
    async selectSplitterVideo() {
        if (!this.isSupported()) {
            throw new Error('File System Access API is not supported in this browser.');
        }

        try {
            // Pick a single file
            const options = {
                types: [
                    {
                        description: 'Video Files',
                        accept: {
                            'video/*': this.supportedExtensions
                        }
                    }
                ]
            };

            // Using the File System Access API to select a file
            const fileHandle = await window.showOpenFilePicker(options).then(handles => handles[0]);
            const file = await fileHandle.getFile();

            return {
                name: file.name,
                file: file,
                handle: fileHandle,
                size: file.size
            };
        } catch (error) {
            if (error.name === 'AbortError') {
                return null; // User cancelled selection
            }
            throw error;
        }
    }

    /**
     * Remove the splitter video reference
     */
    removeSplitterVideo() {
        // Just a placeholder method for API consistency
    }
}
